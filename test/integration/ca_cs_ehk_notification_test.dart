import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/models/onboarding_notification_model.dart';
import '../mocks/firebase_messaging_mock.dart';

/// Comprehensive test suite for CA/CS/EHK train journey notifications
///
/// Test Coverage:
/// 1. Multi-station proximity testing (ARA → BTA → DNR → PNBE → RJPB → PNC)
/// 2. Multi-coach assignment testing (A1 and B3 coaches)
/// 3. Anti-duplication logic testing via Firestore sentAlerts
/// 4. No passenger activity scenarios
/// 5. Real-time notification delivery and status tracking
///
/// Firebase Configuration:
/// - Account: <EMAIL>
/// - Project: RailwaysApp-Prod (ID: railwaysapp-prod)
/// - Custom sound: railops_alarm
/// - Notification channels: railops_alerts
void main() {
  group('CA/CS/EHK Train Journey Notification Tests', () {
    late Map<String, dynamic> testConfig;

    setUpAll(() async {
      // Initialize test configuration
      testConfig = {
        'test_route': ['ARA', 'BTA', 'DNR', 'PNBE', 'RJPB', 'PNC'],
        'test_coordinates': {
          'new_delhi': {'lat': '28.6139', 'lng': '77.2090'},
          'ara': {'lat': '25.5500', 'lng': '84.6667'},
          'bta': {'lat': '25.2167', 'lng': '84.3667'},
          'dnr': {'lat': '25.4167', 'lng': '85.0167'},
          'pnbe': {'lat': '25.5941', 'lng': '85.1376'},
          'rjpb': {'lat': '25.6093', 'lng': '85.1947'},
          'pnc': {'lat': '25.6167', 'lng': '85.2167'},
        },
        'test_coaches': ['A1', 'B3'],
        'ca_user_id': 'test_ca_001',
        'train_number': 'TEST12345',
        'firebase_account': '<EMAIL>',
      };

      // Clear any existing notification history
      await NotificationHistoryStorage.clearHistory();

      if (kDebugMode) {
        print('🧪 CA/CS/EHK Test Suite initialized');
        print('📍 Test route: ${testConfig['test_route']}');
        print('🚂 Test train: ${testConfig['train_number']}');
        print('👤 Test CA: ${testConfig['ca_user_id']}');
      }
    });

    group('Scenario 1: Multi-Station Proximity Testing', () {
      test(
          'Test ARA and BTA both within 50km - should send notifications for both',
          () async {
        if (kDebugMode) {
          print('🧪 Testing Scenario 1.1: ARA and BTA proximity notifications');
        }

        // Simulate being near ARA station
        final araResult = await _testStationProximityNotification(
          stationCode: 'ARA',
          coordinates: testConfig['test_coordinates']['ara'],
          trainNumber: testConfig['train_number'],
          userId: testConfig['ca_user_id'],
          coaches: testConfig['test_coaches'],
          passengerData: {
            'A1': {'onboarding': 5, 'deboarding': 3, 'vacant': 2},
            'B3': {'onboarding': 6, 'deboarding': 3, 'vacant': 6},
          },
        );

        expect(araResult['status'], equals('sent'));
        expect(araResult['stations_notified'], contains('ARA'));

        // Simulate being near BTA station (both ARA and BTA within 50km)
        final btaResult = await _testStationProximityNotification(
          stationCode: 'BTA',
          coordinates: testConfig['test_coordinates']['bta'],
          trainNumber: testConfig['train_number'],
          userId: testConfig['ca_user_id'],
          coaches: testConfig['test_coaches'],
          passengerData: {
            'A1': {'onboarding': 4, 'deboarding': 2, 'vacant': 4},
            'B3': {'onboarding': 7, 'deboarding': 4, 'vacant': 5},
          },
        );

        expect(btaResult['status'], equals('sent'));
        expect(btaResult['stations_notified'], contains('BTA'));

        if (kDebugMode) {
          print('✅ Scenario 1.1 passed: Both ARA and BTA notifications sent');
        }
      });

      test('Test BTA and DNR proximity with anti-duplication logic', () async {
        if (kDebugMode) {
          print('🧪 Testing Scenario 1.2: BTA/DNR with anti-duplication');
        }

        // First call for BTA - should be skipped if no passenger changes
        final btaFirstResult = await _testStationProximityNotification(
          stationCode: 'BTA',
          coordinates: testConfig['test_coordinates']['bta'],
          trainNumber: testConfig['train_number'],
          userId: testConfig['ca_user_id'],
          coaches: testConfig['test_coaches'],
          passengerData: {
            'A1': {'onboarding': 4, 'deboarding': 2, 'vacant': 4},
            'B3': {'onboarding': 7, 'deboarding': 4, 'vacant': 5},
          },
        );

        // Should be skipped due to anti-duplication
        expect(btaFirstResult['status'], equals('skipped'));

        // Second call for BTA with changed passenger details - should send
        final btaChangedResult = await _testStationProximityNotification(
          stationCode: 'BTA',
          coordinates: testConfig['test_coordinates']['bta'],
          trainNumber: testConfig['train_number'],
          userId: testConfig['ca_user_id'],
          coaches: testConfig['test_coaches'],
          passengerData: {
            'A1': {'onboarding': 6, 'deboarding': 2, 'vacant': 2}, // Changed
            'B3': {'onboarding': 8, 'deboarding': 4, 'vacant': 4}, // Changed
          },
        );

        expect(btaChangedResult['status'], equals('sent'));

        // First call for DNR - should send (first time)
        final dnrResult = await _testStationProximityNotification(
          stationCode: 'DNR',
          coordinates: testConfig['test_coordinates']['dnr'],
          trainNumber: testConfig['train_number'],
          userId: testConfig['ca_user_id'],
          coaches: testConfig['test_coaches'],
          passengerData: {
            'A1': {'onboarding': 3, 'deboarding': 5, 'vacant': 2},
            'B3': {'onboarding': 5, 'deboarding': 6, 'vacant': 5},
          },
        );

        expect(dnrResult['status'], equals('sent'));
        expect(dnrResult['stations_notified'], contains('DNR'));

        if (kDebugMode) {
          print(
              '✅ Scenario 1.2 passed: Anti-duplication logic working correctly');
        }
      });
    });

    group('Scenario 2: Multi-Coach Assignment Testing', () {
      test(
          'Test CA assigned to coaches A1 and B3 with proper notification format',
          () async {
        if (kDebugMode) {
          print('🧪 Testing Scenario 2: Multi-coach assignment notifications');
        }

        final result = await _testMultiCoachNotification(
          stationCode: 'DDU',
          coordinates: testConfig['test_coordinates']
              ['new_delhi'], // Using New Delhi as test
          trainNumber: testConfig['train_number'],
          userId: testConfig['ca_user_id'],
          coaches: ['A1', 'B3'],
          passengerData: {
            'A1': {'onboarding': 5, 'deboarding': 3, 'vacant': 2},
            'B3': {'onboarding': 6, 'deboarding': 3, 'vacant': 6},
          },
        );

        expect(result['status'], equals('sent'));
        expect(result['total_coaches'], equals(2));
        expect(result['stations_notified'], contains('DDU'));

        // Verify notification format in console logs
        final notifications =
            await NotificationHistoryStorage.getNotifications();
        final latestNotification = notifications.last;

        expect(latestNotification['title'], contains('DDU'));
        expect(latestNotification['body'], contains('A1'));
        expect(latestNotification['body'], contains('B3'));

        if (kDebugMode) {
          print('✅ Scenario 2 passed: Multi-coach notification format correct');
          print(
              '📊 Expected format: StationCode | Coach | Onboarding | Deboarding | Vacant');
          print('📊 DDU | A1 | 5 | 3 | 2');
          print('📊 DDU | B3 | 6 | 3 | 6');
        }
      });
    });

    group('Scenario 3: No Passenger Activity Testing', () {
      test(
          'Test simplified notification when no passengers boarding/deboarding',
          () async {
        if (kDebugMode) {
          print('🧪 Testing Scenario 3: No passenger activity notifications');
        }

        final result = await _testNoPassengerActivityNotification(
          stationCodes: ['PNBE', 'DNR'],
          coordinates: testConfig['test_coordinates']['pnbe'],
          trainNumber: testConfig['train_number'],
          userId: testConfig['ca_user_id'],
        );

        expect(result['status'], equals('sent'));
        expect(
            result['message'], contains('No passenger onboarding/deboarding'));

        if (kDebugMode) {
          print('✅ Scenario 3 passed: No passenger activity notification sent');
          print(
              '📝 Expected: "No passenger onboarding/deboarding at station PNBE, DNR"');
        }
      });
    });
  });
}

/// Helper function to test station proximity notifications
Future<Map<String, dynamic>> _testStationProximityNotification({
  required String stationCode,
  required Map<String, String> coordinates,
  required String trainNumber,
  required String userId,
  required List<String> coaches,
  required Map<String, Map<String, int>> passengerData,
}) async {
  try {
    // Call the Firebase Cloud Function /notify endpoint
    final result = await FirebaseCloudFunctionService.callNotifyFunction(
      userId: userId,
      trainNumber: trainNumber,
      date: DateTime.now().toIso8601String().split('T')[0],
      lat: coordinates['lat']!,
      lng: coordinates['lng']!,
      fcmToken: 'test_fcm_token_${DateTime.now().millisecondsSinceEpoch}',
    );

    if (kDebugMode) {
      print(
          '📍 Station: $stationCode, Status: ${result['status']}, Message: ${result['message']}');
    }

    return result;
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error testing station $stationCode: $e');
    }
    return {'status': 'error', 'message': e.toString()};
  }
}

/// Helper function to test multi-coach notifications
Future<Map<String, dynamic>> _testMultiCoachNotification({
  required String stationCode,
  required Map<String, String> coordinates,
  required String trainNumber,
  required String userId,
  required List<String> coaches,
  required Map<String, Map<String, int>> passengerData,
}) async {
  return await _testStationProximityNotification(
    stationCode: stationCode,
    coordinates: coordinates,
    trainNumber: trainNumber,
    userId: userId,
    coaches: coaches,
    passengerData: passengerData,
  );
}

/// Helper function to test no passenger activity notifications
Future<Map<String, dynamic>> _testNoPassengerActivityNotification({
  required List<String> stationCodes,
  required Map<String, String> coordinates,
  required String trainNumber,
  required String userId,
}) async {
  try {
    // Simulate a scenario with no passenger activity
    final result = await FirebaseCloudFunctionService.callNotifyFunction(
      userId: userId,
      trainNumber: trainNumber,
      date: DateTime.now().toIso8601String().split('T')[0],
      lat: coordinates['lat']!,
      lng: coordinates['lng']!,
      fcmToken:
          'test_fcm_token_no_activity_${DateTime.now().millisecondsSinceEpoch}',
    );

    if (kDebugMode) {
      print('📍 No activity test for stations: ${stationCodes.join(', ')}');
      print('📍 Status: ${result['status']}, Message: ${result['message']}');
    }

    return result;
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error testing no passenger activity: $e');
    }
    return {'status': 'error', 'message': e.toString()};
  }
}
