import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/services/notification_test_service.dart';
import 'package:railops/services/debug_services/firestore_debug_service.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/models/user_model.dart';

class NotificationTestScreen extends StatefulWidget {
  const NotificationTestScreen({super.key});

  @override
  State<NotificationTestScreen> createState() => _NotificationTestScreenState();
}

class _NotificationTestScreenState extends State<NotificationTestScreen> {
  bool _isRunningTests = false;
  Map<String, dynamic>? _testResults;
  String _statusMessage = 'Ready to run notification tests';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification System Tests'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Notification System Testing',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'This will test the complete notification flow:\n'
                      '• FCM token generation and storage\n'
                      '• Firestore token synchronization\n'
                      '• Cloud Function endpoint connectivity\n'
                      '• End-to-end notification delivery',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Status: $_statusMessage',
                      style: TextStyle(
                        fontSize: 14,
                        color: _isRunningTests ? Colors.orange : Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isRunningTests ? null : _runAllTests,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: _isRunningTests
                        ? const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              ),
                              SizedBox(width: 8),
                              Text('Running Tests...'),
                            ],
                          )
                        : const Text('Run All Tests'),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isRunningTests ? null : _runIndividualTest,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                  ),
                  child: const Text('Individual Tests'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_testResults != null) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              _testResults!['overall_success'] == true
                                  ? Icons.check_circle
                                  : Icons.error,
                              color: _testResults!['overall_success'] == true
                                  ? Colors.green
                                  : Colors.red,
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              'Test Results',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        if (_testResults!['summary'] != null) ...[
                          Text(
                            'Summary: ${_testResults!['summary']['passed_tests']}/${_testResults!['summary']['total_tests']} tests passed '
                            '(${_testResults!['summary']['success_rate']}%)',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 16),
                        ],
                        Expanded(
                          child: SingleChildScrollView(
                            child: _buildTestResultsWidget(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ] else ...[
              const Expanded(
                child: Card(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.notifications_outlined,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'No test results yet',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Run tests to see results here',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runAllTests() async {
    setState(() {
      _isRunningTests = true;
      _statusMessage = 'Running comprehensive notification tests...';
      _testResults = null;
    });

    try {
      final results = await NotificationTestService.runAllTests();
      setState(() {
        _testResults = results;
        _statusMessage = results['overall_success'] == true
            ? 'All tests completed successfully!'
            : 'Some tests failed. Check results below.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Test execution failed: $e';
      });
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  Future<void> _runIndividualTest() async {
    final testOptions = [
      'FCM Token Generation',
      'Firestore Token Storage',
      'Complete Notification Sync',
      'Cloud Function Endpoint',
      'Enhanced Notification Test',
      'Debug Firestore Token Storage',
      'Force Token Refresh',
      'Quick Token Check',
    ];

    final selectedTest = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Test'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: testOptions
              .map((test) => ListTile(
                    title: Text(test),
                    onTap: () => Navigator.of(context).pop(test),
                  ))
              .toList(),
        ),
      ),
    );

    if (selectedTest == null) return;

    setState(() {
      _isRunningTests = true;
      _statusMessage = 'Running $selectedTest test...';
      _testResults = null;
    });

    try {
      Map<String, dynamic> result;
      switch (selectedTest) {
        case 'FCM Token Generation':
          result = await NotificationTestService.testFcmTokenGeneration();
          break;
        case 'Firestore Token Storage':
          result = await NotificationTestService.testFirestoreTokenStorage();
          break;
        case 'Complete Notification Sync':
          result = await NotificationTestService.testCompleteNotificationSync();
          break;
        case 'Cloud Function Endpoint':
          result = await NotificationTestService.testCloudFunctionEndpoint();
          break;
        case 'Enhanced Notification Test':
          result = await _testEnhancedNotifications();
          break;
        case 'Debug Firestore Token Storage':
          result = await FirestoreDebugService.debugTokenStorage();
          break;
        case 'Force Token Refresh':
          final refreshResult = await FirestoreDebugService.forceTokenRefresh();
          result = {
            'success': refreshResult,
            'details': {'refresh_successful': refreshResult},
            'errors': refreshResult ? [] : ['Token refresh failed'],
          };
          break;
        case 'Quick Token Check':
          await FirestoreDebugService.quickTokenCheck();
          result = {
            'success': true,
            'details': {'check_completed': true},
            'errors': [],
          };
          break;
        default:
          throw Exception('Unknown test: $selectedTest');
      }

      setState(() {
        _testResults = {
          'test_suite': 'Individual Test',
          'overall_success': result['success'],
          'tests': {selectedTest: result},
          'summary': {
            'total_tests': 1,
            'passed_tests': result['success'] ? 1 : 0,
            'failed_tests': result['success'] ? 0 : 1,
            'success_rate': result['success'] ? '100.0' : '0.0',
          },
        };
        _statusMessage = result['success']
            ? '$selectedTest completed successfully!'
            : '$selectedTest failed. Check results below.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Test execution failed: $e';
      });
    } finally {
      setState(() {
        _isRunningTests = false;
      });
    }
  }

  /// Test the enhanced notification system with real coordinates
  Future<Map<String, dynamic>> _testEnhancedNotifications() async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final userId = userModel.userName;
      final userTrainNumber = userModel.trainNo;
      final userSelectedDate = userModel.selectedDate;

      // Use fallback values if user data is missing
      final trainNumber =
          userTrainNumber.isNotEmpty ? userTrainNumber : '12345';
      final selectedDate = userSelectedDate.isNotEmpty
          ? userSelectedDate
          : DateTime.now()
              .toIso8601String()
              .split('T')[0]; // Today's date in yyyy-mm-dd format

      final bool usingFallbackTrain = userTrainNumber.isEmpty;
      final bool usingFallbackDate = userSelectedDate.isEmpty;

      if (userId.isEmpty) {
        return {
          'success': false,
          'details': {
            'error': 'Missing user ID',
            'user_id': userId,
            'train_number': trainNumber,
            'date': selectedDate,
          },
          'errors': [
            'User ID is required but missing. Please ensure you are logged in.'
          ],
        };
      }

      // Test with New Delhi coordinates
      const testLat = '28.6139';
      const testLng = '77.2090';

      print('🧪 Testing enhanced notifications for user: $userId');
      print(
          '🧪 Train number: $trainNumber ${usingFallbackTrain ? '(fallback)' : '(user data)'}');
      print(
          '🧪 Date: $selectedDate ${usingFallbackDate ? '(fallback)' : '(user data)'}');
      print('🧪 Test coordinates: lat=$testLat, lng=$testLng');

      // Call the enhanced Cloud Function
      final result = await FirebaseCloudFunctionService.callNotifyFunction(
        userId: userId,
        trainNumber: trainNumber,
        date: selectedDate,
        lat: testLat,
        lng: testLng,
      );

      print('🧪 Enhanced notification result: $result');

      final success =
          result['status'] == 'sent' || result['status'] == 'skipped';

      return {
        'success': success,
        'details': {
          'cloud_function_status': result['status'] ?? 'unknown',
          'message': result['message'] ?? 'No message',
          'user_id': userId,
          'train_number': trainNumber,
          'date': selectedDate,
          'using_fallback_train': usingFallbackTrain,
          'using_fallback_date': usingFallbackDate,
          'original_user_train': userTrainNumber,
          'original_user_date': userSelectedDate,
          'test_coordinates': '$testLat, $testLng',
          'timestamp': DateTime.now().toIso8601String(),
          'full_response': result.toString(),
        },
        'errors': success
            ? []
            : ['Cloud Function returned error status: ${result['status']}'],
      };
    } catch (e) {
      print('🧪 Error testing enhanced notifications: $e');
      return {
        'success': false,
        'details': {
          'error': e.toString(),
          'timestamp': DateTime.now().toIso8601String(),
        },
        'errors': ['Exception during enhanced notification test: $e'],
      };
    }
  }

  Widget _buildTestResultsWidget() {
    if (_testResults == null || _testResults!['tests'] == null) {
      return const Text('No test data available');
    }

    final tests = _ensureMapStringDynamic(_testResults!['tests']);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: tests.entries.map((entry) {
        final testName = entry.key;
        final testData = _ensureMapStringDynamic(entry.value);
        final success = testData['success'] as bool? ?? false;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ExpansionTile(
            leading: Icon(
              success ? Icons.check_circle : Icons.error,
              color: success ? Colors.green : Colors.red,
            ),
            title: Text(
              testName,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: success ? Colors.green : Colors.red,
              ),
            ),
            subtitle: Text(
              success ? 'PASSED' : 'FAILED',
              style: TextStyle(
                color: success ? Colors.green : Colors.red,
                fontSize: 12,
              ),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (testData['details'] != null) ...[
                      const Text(
                        'Details:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDetails(
                            _ensureMapStringDynamic(testData['details'])),
                        style: const TextStyle(
                            fontSize: 12, fontFamily: 'monospace'),
                      ),
                    ],
                    if (testData['errors'] != null &&
                        _ensureList(testData['errors']).isNotEmpty) ...[
                      const SizedBox(height: 8),
                      const Text(
                        'Errors:',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, color: Colors.red),
                      ),
                      const SizedBox(height: 4),
                      ..._ensureList(testData['errors']).map((error) => Text(
                            '• $error',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.red),
                          )),
                    ],
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  String _formatDetails(Map<String, dynamic> details) {
    final buffer = StringBuffer();
    details.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    return buffer.toString();
  }

  /// Helper method to safely convert dynamic Map to Map<String, dynamic>
  /// This prevents type casting errors when processing test results
  Map<String, dynamic> _ensureMapStringDynamic(dynamic input) {
    if (input == null) {
      return <String, dynamic>{};
    }

    if (input is Map<String, dynamic>) {
      return input;
    }

    if (input is Map) {
      // Convert Map<dynamic, dynamic> to Map<String, dynamic>
      final result = <String, dynamic>{};
      input.forEach((key, value) {
        result[key.toString()] = value;
      });
      return result;
    }

    // If it's not a Map at all, return a safe representation
    return <String, dynamic>{'value': input.toString()};
  }

  /// Helper method to safely convert dynamic input to List
  /// This prevents type casting errors when processing error lists
  List<dynamic> _ensureList(dynamic input) {
    if (input == null) {
      return <dynamic>[];
    }

    if (input is List) {
      return input;
    }

    // If it's not a List, wrap it in a list
    return [input];
  }
}
